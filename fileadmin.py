import os
from aiogram import types, Router
from aiogram.filters import Command
from db import list_all_users
import logging

router = Router()
logger = logging.getLogger(__name__)

# Получаем ID администраторов из переменных окружения
def get_admin_ids():
    """Получает список ID администраторов из переменных окружения"""
    admin_ids_str = os.getenv("ADMIN_IDS", "")
    if admin_ids_str:
        try:
            return [int(id.strip()) for id in admin_ids_str.split(",") if id.strip()]
        except ValueError as e:
            logger.error(f"Error parsing ADMIN_IDS: {e}")
            return []
    return []

ADMIN_IDS = get_admin_ids()

@router.message(Command("adminpanel"))
async def admin_panel(message: types.Message):
    if not ADMIN_IDS:
        await message.answer("❌ Администраторы не настроены. Установите переменную окружения ADMIN_IDS.")
        return

    if message.from_user.id not in ADMIN_IDS:
        await message.answer("❌ У вас нет доступа к админ-панели.")
        return

    try:
        users = list_all_users()
        if not users:
            await message.answer("⛔️ Пока никто не зарегистрирован.")
        else:
            text = "\n\n".join(
                f"👤 {u['first_name'] or 'Без имени'} (@{u['username'] or '—'})\n"
                f"🆔 {u['telegram_id']}\n"
                f"📁 Папка: {u['drive_folder_id'] or 'Не создана'}"
                for u in users
            )
            await message.answer(f"📋 Зарегистрированные пользователи ({len(users)}):\n\n{text}")
            logger.info(f"Admin panel accessed by user {message.from_user.id}")
    except Exception as e:
        logger.error(f"Error in admin panel: {e}")
        await message.answer("❌ Ошибка при получении списка пользователей.")
