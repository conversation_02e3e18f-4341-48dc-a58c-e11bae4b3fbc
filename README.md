# GPT Obsidian Bot

Telegram-бот для автоматизации приёма, классификации и сохранения заметок в Obsidian через Google Drive.

## 🚀 Возможности

- 📝 Приём заметок через Telegram
- 🧠 Автоматическая классификация заметок с помощью GPT-4o
- 📁 Сохранение в структурированные папки на Google Drive
- 👥 Система регистрации пользователей
- 🔐 Админ-панель для управления пользователями

## 📋 Требования

- Python 3.8+
- Telegram Bot Token
- OpenAI API Key
- Google Drive API доступ

## 🛠 Установка

1. **Клонируйте репозиторий:**
   ```bash
   git clone <repository-url>
   cd gpt_obsidian_bot
   ```

2. **Установите зависимости:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Настройте переменные окружения:**

   Создайте файл `.env` на основе `.env.example`:
   ```bash
   cp .env.example .env
   ```

   Заполните `.env` файл:
   ```env
   TELEGRAM_TOKEN=your_telegram_bot_token
   OPENAI_API_KEY=your_openai_api_key
   ADMIN_IDS=your_telegram_user_id
   ```

4. **Настройте Google Drive API:**
   - Создайте проект в Google Cloud Console
   - Включите Google Drive API
   - Создайте credentials и скачайте `client_secrets.json`
   - Поместите файл в корень проекта

5. **Запустите бота:**
   ```bash
   python bot.py
   ```

## 🔧 Конфигурация

### Переменные окружения (.env)

- `TELEGRAM_TOKEN` - токен Telegram бота
- `OPENAI_API_KEY` - API ключ OpenAI
- `ADMIN_IDS` - ID администраторов (через запятую)

### Файл конфигурации (config.json)

Альтернативный способ настройки (менее безопасный):
```json
{
  "telegram_token": "YOUR_TELEGRAM_BOT_TOKEN",
  "openai_api_key": "YOUR_OPENAI_API_KEY",
  "vault_path": "drive://ObsidianNotes",
  "default_language": "ru"
}
```

## 📁 Структура проекта

```
gpt_obsidian_bot/
├── bot.py              # Основной файл бота
├── db.py               # Работа с базой данных
├── fileadmin.py        # Админ-панель
├── config.json         # Конфигурация
├── requirements.txt    # Зависимости
├── .env.example        # Пример переменных окружения
├── gdrive/
│   ├── drive_auth.py   # Аутентификация Google Drive
│   └── drive_uploader.py # Загрузка файлов
├── utils/
│   ├── classifier.py   # Классификация заметок
│   ├── md_writer.py    # Создание Markdown файлов
│   └── mover.py        # Перемещение файлов
└── vault/              # Локальное хранилище заметок
```

## 🎯 Использование

1. **Регистрация:** Отправьте `/start` боту
2. **Создание заметки:** Просто отправьте текст боту
3. **Админ-панель:** Используйте `/adminpanel` (только для администраторов)

## 🔒 Безопасность

- ✅ API ключи вынесены в переменные окружения
- ✅ Файлы с секретами добавлены в `.gitignore`
- ✅ Валидация входных данных
- ✅ Логирование ошибок

## 🐛 Исправленные проблемы

- ✅ Исправлено несоответствие схем базы данных
- ✅ Убран дублированный код в classifier.py
- ✅ Обновлен API OpenAI до современной версии
- ✅ Добавлена интеграция с Google Drive
- ✅ Улучшена обработка ошибок
- ✅ Вынесены API ключи в переменные окружения

## 📝 Лицензия

MIT License