#!/usr/bin/env python3
"""
Тестовый скрипт для проверки основных функций бота
"""

import os
import sys
import logging
from pathlib import Path

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_imports():
    """Тестирует импорты всех модулей"""
    print("🔍 Тестирование импортов...")
    
    try:
        import aiogram
        print("✅ aiogram импортирован")
    except ImportError as e:
        print(f"❌ aiogram: {e}")
        return False
    
    try:
        import openai
        print("✅ openai импортирован")
    except ImportError as e:
        print(f"❌ openai: {e}")
        return False
    
    try:
        from pydrive2.auth import GoogleAuth
        print("✅ pydrive2 импортирован")
    except ImportError as e:
        print(f"❌ pydrive2: {e}")
        return False
    
    try:
        from dotenv import load_dotenv
        print("✅ python-dotenv импортирован")
    except ImportError as e:
        print(f"❌ python-dotenv: {e}")
        return False
    
    return True

def test_config():
    """Тестирует конфигурацию"""
    print("\n🔍 Тестирование конфигурации...")
    
    # Проверка .env файла
    if not os.path.exists('.env'):
        print("❌ Файл .env не найден")
        return False
    
    from dotenv import load_dotenv
    load_dotenv()
    
    telegram_token = os.getenv('TELEGRAM_TOKEN')
    openai_key = os.getenv('OPENAI_API_KEY')
    
    if not telegram_token or telegram_token == 'your_telegram_bot_token':
        print("❌ TELEGRAM_TOKEN не настроен в .env")
        return False
    
    if not openai_key or openai_key == 'your_openai_api_key':
        print("❌ OPENAI_API_KEY не настроен в .env")
        return False
    
    print("✅ Переменные окружения настроены")
    return True

def test_database():
    """Тестирует базу данных"""
    print("\n🔍 Тестирование базы данных...")
    
    try:
        from db import init_db, is_registered
        init_db()
        print("✅ База данных инициализирована")
        
        # Тест функции проверки регистрации
        result = is_registered(12345)  # Тестовый ID
        print(f"✅ Функция is_registered работает: {result}")
        
        return True
    except Exception as e:
        print(f"❌ Ошибка базы данных: {e}")
        return False

def test_classifier():
    """Тестирует классификатор заметок"""
    print("\n🔍 Тестирование классификатора...")
    
    try:
        from utils.classifier import classify_note
        from dotenv import load_dotenv
        load_dotenv()
        
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key or api_key == 'your_openai_api_key':
            print("⚠️  Пропуск теста классификатора - API ключ не настроен")
            return True
        
        # Тест с простой заметкой
        test_note = "Сегодня думал о смысле жизни"
        category = classify_note(test_note, api_key)
        print(f"✅ Классификатор работает: '{test_note}' -> '{category}'")
        
        return True
    except Exception as e:
        print(f"❌ Ошибка классификатора: {e}")
        return False

def test_markdown_writer():
    """Тестирует создание markdown файлов"""
    print("\n🔍 Тестирование markdown writer...")
    
    try:
        from utils.md_writer import save_to_markdown
        
        filename, content = save_to_markdown(
            prompt="Тестовая заметка",
            response="Тестовый ответ",
            category="тест"
        )
        
        print(f"✅ Markdown файл создан: {filename}")
        
        # Проверяем, что файл создался
        file_path = Path("vault/тест") / filename
        if file_path.exists():
            print("✅ Файл сохранен на диск")
            # Удаляем тестовый файл
            file_path.unlink()
            print("✅ Тестовый файл удален")
        
        return True
    except Exception as e:
        print(f"❌ Ошибка markdown writer: {e}")
        return False

def main():
    """Основная функция тестирования"""
    print("🧪 Запуск тестов GPT Obsidian Bot")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_config,
        test_database,
        test_classifier,
        test_markdown_writer
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Неожиданная ошибка в тесте: {e}")
    
    print(f"\n📊 Результаты: {passed}/{total} тестов пройдено")
    
    if passed == total:
        print("🎉 Все тесты пройдены! Бот готов к запуску.")
        return True
    else:
        print("⚠️  Некоторые тесты не пройдены. Проверьте конфигурацию.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
