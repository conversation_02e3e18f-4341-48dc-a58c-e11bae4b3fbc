from pydrive2.auth import GoogleAuth
from pydrive2.drive import GoogleDrive
import logging

logger = logging.getLogger(__name__)

# Инициализация Google Drive
def init_drive():
    """Инициализирует подключение к Google Drive"""
    try:
        gauth = GoogleAuth()
        gauth.LocalWebserverAuth()
        return GoogleDrive(gauth)
    except Exception as e:
        logger.error(f"Failed to initialize Google Drive: {e}")
        raise

# Глобальная переменная для drive
drive = None

def get_drive():
    """Получает экземпляр Google Drive, инициализируя при необходимости"""
    global drive
    if drive is None:
        drive = init_drive()
    return drive

def get_or_create_obsidian_root():
    """Получает или создает корневую папку ObsidianNotes"""
    try:
        drive_instance = get_drive()
        folder_list = drive_instance.ListFile({
            'q': "title='ObsidianNotes' and mimeType='application/vnd.google-apps.folder' and trashed=false"
        }).GetList()

        if folder_list:
            logger.info("Found existing ObsidianNotes folder")
            return folder_list[0]
        else:
            logger.info("Creating new ObsidianNotes folder")
            folder = drive_instance.CreateFile({
                'title': 'ObsidianNotes',
                'mimeType': 'application/vnd.google-apps.folder'
            })
            folder.Upload()
            return folder
    except Exception as e:
        logger.error(f"Error getting/creating ObsidianNotes folder: {e}")
        raise

def create_user_folder(user_id):
    """Создает папку пользователя в Google Drive"""
    try:
        drive_instance = get_drive()
        obsidian_root = get_or_create_obsidian_root()
        folder_title = f'User_{user_id}'

        existing = drive_instance.ListFile({
            'q': f"'{obsidian_root['id']}' in parents and title='{folder_title}' and mimeType='application/vnd.google-apps.folder' and trashed=false"
        }).GetList()

        if existing:
            logger.info(f"Found existing folder for user {user_id}")
            return existing[0]['id']
        else:
            logger.info(f"Creating new folder for user {user_id}")
            folder = drive_instance.CreateFile({
                'title': folder_title,
                'parents': [{'id': obsidian_root['id']}],
                'mimeType': 'application/vnd.google-apps.folder'
            })
            folder.Upload()
            return folder['id']
    except Exception as e:
        logger.error(f"Error creating user folder for {user_id}: {e}")
        raise

def upload_to_drive(filename, content, category, user_folder_id):
    """Загружает файл в Google Drive в соответствующую категорию"""
    try:
        drive_instance = get_drive()

        # Найти или создать папку категории в папке пользователя
        sub_folders = drive_instance.ListFile({
            'q': f"'{user_folder_id}' in parents and title='{category}' and mimeType='application/vnd.google-apps.folder' and trashed=false"
        }).GetList()

        if sub_folders:
            category_folder = sub_folders[0]
            logger.info(f"Found existing category folder: {category}")
        else:
            logger.info(f"Creating new category folder: {category}")
            category_folder = drive_instance.CreateFile({
                'title': category,
                'parents': [{'id': user_folder_id}],
                'mimeType': 'application/vnd.google-apps.folder'
            })
            category_folder.Upload()

        # Создаем и загружаем файл
        file_drive = drive_instance.CreateFile({
            'title': filename,
            'parents': [{'id': category_folder['id']}]
        })
        file_drive.SetContentString(content)
        file_drive.Upload()

        logger.info(f"Successfully uploaded file: {filename}")
        return file_drive['alternateLink']

    except Exception as e:
        logger.error(f"Error uploading file {filename}: {e}")
        raise
