import os
from datetime import datetime

def save_to_markdown(prompt, response, category="Unsorted", title=None):
    # Генерируем имя файла
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M")
    safe_title = title or prompt[:30].replace(" ", "_").replace("?", "")
    filename = f"{timestamp}_{safe_title}.md"

    # Путь к папке
    folder = os.path.join("vault", category)
    os.makedirs(folder, exist_ok=True)
    filepath = os.path.join(folder, filename)

    # Содержимое Markdown
    content = f"""---
title: "{title or safe_title}"
tags: [{category}]
created: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
category: {category}
---

# {title or safe_title}

**Prompt:**
{prompt}

**Response:**
{response}
"""

    # Сохраняем
    with open(filepath, "w", encoding="utf-8") as f:
        f.write(content)

    return filename, content
