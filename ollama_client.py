import requests # Импортируем библиотеку для выполнения HTTP-запросов
import json     # Импортируем библиотеку для работы с JSON-данными

def generate_response(prompt, model_name="mistral"):
    """
    Отправляет промпт (запрос) в локально запущенную модель Ollama
    и возвращает сгенерированный ответ.

    :param prompt: Текстовый запрос, который нужно отправить модели.
    :param model_name: Имя модели в Ollama (например, "mistral", "gemma3:4b"). По умолчанию "mistral".
    :return: Сгенерированный текст ответа модели или None в случае ошибки.
    """
    url = "http://localhost:11434/api/generate" # URL локального Ollama API для генерации текста
    headers = {"Content-Type": "application/json"} # Указываем, что отправляем JSON
    data = {                                  # Данные, которые будут отправлены в теле запроса
        "model": model_name,                  # Имя модели, которую хотим использовать
        "prompt": prompt,                     # Сам текстовый запрос (промпт)
        "stream": False                       # False означает, что ждем полный ответ сразу, а не по частям
    }
    try:
        # Отправляем POST-запрос на API Ollama
        response = requests.post(url, headers=headers, data=json.dumps(data))
        response.raise_for_status() # Проверяем статус HTTP-ответа (вызывает ошибку, если статус 4xx или 5xx)
        result = response.json()    # Парсим JSON-ответ от Ollama
        return result["response"]   # Возвращаем только сгенерированный текст
    except requests.exceptions.RequestException as e:
        # Обработка ошибок, если не удалось подключиться к Ollama или получить ответ
        print(f"Ошибка при обращении к Ollama API: {e}")
        return None

if __name__ == "__main__":
    # Этот блок кода выполняется только при запуске файла напрямую (не при импорте)
    # Он служит для демонстрации работы функции generate_response

    user_note = "Вчера на городском собрании обсуждались планы по развитию новой городской библиотеки и реконструкции старого парка. Жители выразили опасения по поводу вырубки деревьев и предложили альтернативные места для строительства."
    
    # Формулируем промпт для классификации заметки
    prompt_for_classification = f"Определи основную тему следующей заметки, ответь одним словом: \"{user_note}\""
    
    # Вызываем функцию для получения ответа от модели "mistral"
    # Важно: для работы этого блока Ollama должна быть запущена и модель "mistral" должна быть загружена.
    response_text = generate_response(prompt_for_classification, model_name="mistral") 
    
    if response_text:
        print(f"Тема заметки: {response_text.strip()}")
    else:
        print("Не удалось получить ответ от LLM.")