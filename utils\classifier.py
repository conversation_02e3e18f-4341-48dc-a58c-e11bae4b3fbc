from openai import OpenAI
import logging

logger = logging.getLogger(__name__)

def classify_note(user_input: str, api_key: str) -> str:
    """
    Классифицирует заметку пользователя в одну из предопределенных категорий.

    Args:
        user_input: Текст заметки пользователя
        api_key: API ключ OpenAI

    Returns:
        Название категории для сохранения заметки
    """
    try:
        client = OpenAI(api_key=api_key)

        system_prompt = (
            "Ты — помощник по организации заметок.\n"
            "Проанализируй текст и предложи краткое имя папки (1 слово на русском), "
            "куда следует сохранить эту заметку в Obsidian.\n"
            "Используй категории: философия, тело, мышление, цели, творчество, дневник, отношения, проекты, идеи.\n"
            "Если не уверен — ответь: Unsorted.\n"
        )

        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_input}
            ],
            temperature=0.3,
            max_tokens=50
        )

        folder = response.choices[0].message.content.strip()

        # Валидация результата
        valid_categories = [
            "философия", "тело", "мышление", "цели", "творчество",
            "дневник", "отношения", "проекты", "идеи", "Unsorted"
        ]

        if folder not in valid_categories:
            logger.warning(f"Invalid category '{folder}' returned, using 'Unsorted'")
            folder = "Unsorted"

        return folder

    except Exception as e:
        logger.error(f"Error classifying note: {e}")
        return "Unsorted"
