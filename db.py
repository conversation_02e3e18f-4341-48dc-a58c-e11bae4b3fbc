import sqlite3

# Имя файла базы
DB_NAME = "users.db"

# 1. Инициализация базы данных
def init_db():
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            telegram_id INTEGER PRIMARY KEY,
            username TEXT,
            first_name TEXT,
            last_name TEXT,
            drive_folder_id TEXT,
            registered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    conn.commit()
    conn.close()

# 2. Проверка, зарегистрирован ли пользователь
def is_registered(telegram_id: int) -> bool:
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()
    cursor.execute('SELECT 1 FROM users WHERE telegram_id = ?', (telegram_id,))
    result = cursor.fetchone()
    conn.close()
    return result is not None

# 3. Регистрация нового пользователя
def register_user(user, folder_id: str):
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()
    cursor.execute('''
        INSERT OR REPLACE INTO users (
            telegram_id, username, first_name, last_name, drive_folder_id
        ) VALUES (?, ?, ?, ?, ?)
    ''', (
        user.id,
        user.username,
        user.first_name,
        user.last_name,
        folder_id
    ))
    conn.commit()
    conn.close()

# 4. Получение ID папки пользователя на Google Диске
def get_drive_folder_id(telegram_id: int) -> str:
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()
    cursor.execute('SELECT drive_folder_id FROM users WHERE telegram_id = ?', (telegram_id,))
    result = cursor.fetchone()
    conn.close()
    return result[0] if result else None
def list_all_users():
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()
    cursor.execute("SELECT telegram_id, username, first_name, drive_folder_id FROM users")
    rows = cursor.fetchall()
    conn.close()
    return [
        {
            "telegram_id": row[0],
            "username": row[1],
            "first_name": row[2],
            "drive_folder_id": row[3]
        }
        for row in rows
    ]
