---
title: "Результаты_работы_над_проектом"
tags: [Проекты]
created: 2025-05-18 21:13:08
category: Проекты
---

# Результаты_работы_над_проектом

**Prompt:**
Результаты работы над проектом GPT Obsidian Bot
Период: май 2025
Проект: Telegram-бот + OpenAI + Google Drive
Цель: автоматизация приёма, классификации и сохранения заметок в Obsidian через Telegram

Выполненные задачи:
Аутентификация через Google OAuth
Реализована система авторизации для доступа к Google Drive.
Пройден весь путь от создания client_secrets.json до успешной авторизации через локальный сервер.

Интеграция с Google Drive
Реализована загрузка Markdown-файлов в заданную папку на Google Диске.
Исправлены ошибки, связанные с неактивным API — включён Google Drive API в консоли разработчика.

Интеграция с OpenAI
Использована модель GPT-4o для классификации пользовательских текстов.
Настроен API-ключ, добавлен файл конфигурации config.json, реализована генерация категорий.

Классификация заметок и структура хранения
Заметки автоматически распределяются по категориям (философия, отношения, идеи и др.).
Добавлена возможность создания новых папок по категориям по запросу модели.

Telegram-бот

Обработка входящих сообщений

Генерация Markdown-файлов

Ответ с готовой ссылкой на Google Drive

Регистрация пользователей

Реализована регистрация по команде /start

Пользовательские данные сохраняются в users.json

Полученные знания и навыки:
Понимание работы OAuth 2.0 и настройки доступа к Google API

Работа с aiogram 3.x, диспетчером событий, асинхронными функциями и обработкой ошибок

Использование OpenAI API (версия 1.0+)

Создание логики Telegram-бота с нуля до рабочего продукта

Последовательная настройка и отладка цепочки Telegram → GPT → Obsidian

Углубление навыков работы с JSON, структурой проекта и архитектурой кода

Понимание важности поэтапного тестирования и логирования

Потенциальные направления развития:
Добавление интерактивной регистрации через кнопки

Переход от хранения в JSON к базе данных (например, SQLite или Firestore)

Улучшение логики категоризации с предложениями от модели

Поддержка мультиязычной обработки и различных форматов ввода (аудио, голос, файлы)

"Этот проект — не просто интеграция API. Это построенный мост между мышлением и автоматизацией."

**Response:**
Ответ будет здесь
