#!/usr/bin/env python3
"""
Setup script for GPT Obsidian Bot
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(command, description):
    """Выполняет команду и выводит результат"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} - успешно")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - ошибка:")
        print(f"   {e.stderr}")
        return False

def check_python_version():
    """Проверяет версию Python"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Требуется Python 3.8 или выше")
        print(f"   Текущая версия: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✅ Python версия: {version.major}.{version.minor}.{version.micro}")
    return True

def setup_environment():
    """Настраивает окружение"""
    print("🚀 Настройка GPT Obsidian Bot")
    print("=" * 50)
    
    # Проверка версии Python
    if not check_python_version():
        return False
    
    # Установка зависимостей
    if not run_command("pip install -r requirements.txt", "Установка зависимостей"):
        return False
    
    # Создание .env файла если его нет
    if not os.path.exists('.env'):
        if os.path.exists('.env.example'):
            shutil.copy('.env.example', '.env')
            print("✅ Создан файл .env из .env.example")
            print("⚠️  Не забудьте заполнить .env файл своими API ключами!")
        else:
            print("❌ Файл .env.example не найден")
            return False
    else:
        print("✅ Файл .env уже существует")
    
    # Создание папки vault если её нет
    vault_path = Path("vault")
    if not vault_path.exists():
        vault_path.mkdir()
        print("✅ Создана папка vault")
    
    # Инициализация базы данных
    try:
        from db import init_db
        init_db()
        print("✅ База данных инициализирована")
    except Exception as e:
        print(f"❌ Ошибка инициализации базы данных: {e}")
        return False
    
    print("\n🎉 Настройка завершена!")
    print("\n📋 Следующие шаги:")
    print("1. Заполните файл .env своими API ключами")
    print("2. Настройте Google Drive API (client_secrets.json)")
    print("3. Запустите бота: python bot.py")
    
    return True

if __name__ == "__main__":
    success = setup_environment()
    sys.exit(0 if success else 1)
