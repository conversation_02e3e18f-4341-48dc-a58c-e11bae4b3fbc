import asyncio
import json
import logging
import os
from aiogram import <PERSON><PERSON>, Di<PERSON>atcher, types
from aiogram.filters import Command
from utils.md_writer import save_to_markdown
from ollama_client import generate_response
from gdrive.drive_uploader import upload_to_drive, create_user_folder
from db import init_db, is_registered, register_user, get_drive_folder_id
from fileadmin import router as admin_router
from dotenv import load_dotenv

# Загрузка переменных окружения
load_dotenv()

# Настройка логирования
logging.basicConfig(level=logging.INFO, filename="bot.log")
logger = logging.getLogger(__name__)

# Константы
MESSAGES = {
    "ALREADY_REGISTERED": "✅ Уже зарегистрирован.",
    "SUCCESS_REGISTER": "✅ Вы успешно зарегистрированы. Можно писать заметки!",
    "NOT_REGISTERED": "❌ Пожалуйста, введите /start для регистрации.",
    "INVALID_INPUT": "❌ Заметка пустая или слишком длинная.",
    "SAVE_ERROR": "❌ Ошибка при сохранении заметки.",
    "REGISTRATION_ERROR": "❌ Ошибка при регистрации. Попробуйте позже."
}

# Загрузка конфигурации
def load_config():
    """Загружает конфигурацию из переменных окружения и config.json"""
    config = {}

    # Приоритет: переменные окружения > config.json
    telegram_token = os.getenv("TELEGRAM_TOKEN")
    openai_api_key = os.getenv("OPENAI_API_KEY")

    if not telegram_token or not openai_api_key:
        try:
            with open("config.json", encoding="utf-8") as f:
                file_config = json.load(f)
                telegram_token = telegram_token or file_config.get("telegram_token")
                openai_api_key = openai_api_key or file_config.get("openai_api_key")
                config.update(file_config)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.error(f"Config loading error: {e}")
            raise

    if not telegram_token:
        raise ValueError("TELEGRAM_TOKEN not found in environment variables or config.json")
    # OpenAI API key is now optional since we're using Ollama for classification
    # if not openai_api_key:
    #     raise ValueError("OPENAI_API_KEY not found in environment variables or config.json")

    config["telegram_token"] = telegram_token
    config["openai_api_key"] = openai_api_key
    return config

try:
    config = load_config()
except Exception as e:
    logger.error(f"Configuration error: {e}")
    raise

bot = Bot(token=config["telegram_token"])
dp = Dispatcher()
dp.include_router(admin_router)

@dp.message(Command("start"))
async def handle_start(message: types.Message) -> None:
    user_id = message.from_user.id
    if is_registered(user_id):
        await message.answer(MESSAGES["ALREADY_REGISTERED"])
    else:
        try:
            # Создаем папку пользователя в Google Drive
            folder_id = create_user_folder(user_id)
            # Регистрируем пользователя с папкой
            register_user(message.from_user, folder_id)
            await message.answer(MESSAGES["SUCCESS_REGISTER"])
            logger.info(f"User {user_id} registered successfully with folder {folder_id}")
        except Exception as e:
            logger.error(f"Registration error for user {user_id}: {e}")
            await message.answer(MESSAGES["REGISTRATION_ERROR"])

@dp.message()
async def handle_message(message: types.Message) -> None:
    user_id = message.from_user.id
    if not is_registered(user_id):
        await message.answer(MESSAGES["NOT_REGISTERED"])
        return

    user_input = message.text.strip()
    if not user_input or len(user_input) > 1000:
        await message.answer(MESSAGES["INVALID_INPUT"])
        return

    try:
        # Получаем ID папки пользователя
        user_folder_id = get_drive_folder_id(user_id)
        if not user_folder_id:
            logger.error(f"No folder found for user {user_id}")
            await message.answer("❌ Ошибка: папка пользователя не найдена. Попробуйте /start")
            return

        # Классифицируем заметку с помощью локальной LLM через Ollama
        classification_prompt = (
            f"Проанализируй текст и предложи краткое имя папки (1 слово на русском), "
            f"куда следует сохранить эту заметку в Obsidian. "
            f"Используй категории: философия, тело, мышление, цели, творчество, дневник, отношения, проекты, идеи. "
            f"Если не уверен — ответь: Unsorted. "
            f"Только название категории, без лишних слов: \"{user_input}\""
        )

        category = generate_response(classification_prompt, model_name="mistral")

        # Проверяем, что получили ответ от Ollama
        if category is None:
            await message.answer("❌ Ошибка классификации заметки. Проверьте, что Ollama запущена.")
            return

        # Очищаем категорию от лишних пробелов и переносов строк
        category = category.strip()

        # Валидация результата
        valid_categories = [
            "философия", "тело", "мышление", "цели", "творчество",
            "дневник", "отношения", "проекты", "идеи", "Unsorted"
        ]

        if category not in valid_categories:
            logger.warning(f"Invalid category '{category}' returned from Ollama, using 'Unsorted'")
            category = "Unsorted"

        # Создаем markdown файл
        filename, content = save_to_markdown(
            prompt=user_input,
            response="Ответ будет здесь",
            category=category
        )

        # Загружаем в Google Drive
        file_url = upload_to_drive(filename, content, category, user_folder_id)

        await message.answer(
            f"✅ Заметка сохранена в категорию **{category}**.\n📄 [Ссылка на файл]({file_url})",
            parse_mode="Markdown"
        )
        logger.info(f"Note saved for user {user_id} in category {category}")

    except Exception as e:
        logger.error(f"Error processing note for user {user_id}: {e}")
        await message.answer(MESSAGES["SAVE_ERROR"])

async def main():
    init_db()
    await bot.delete_webhook(drop_pending_updates=True)
    await dp.start_polling(bot)

if __name__ == "__main__":
    asyncio.run(main())